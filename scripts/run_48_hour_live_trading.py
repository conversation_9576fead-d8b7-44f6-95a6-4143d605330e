#!/usr/bin/env python3
"""
48-Hour Live Trading Session with Signature Verification Fixes
This script runs a comprehensive 48-hour live trading session with:
- Jito signature verification fixes
- Real-time monitoring and alerts
- Dashboard integration
- Performance tracking
- Emergency stop mechanisms
"""

import os
import sys
import asyncio
import logging
import time
import signal
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json

# Add project root to path
sys.path.append('.')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/48_hour_live_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTrading48Hour:
    """48-hour live trading session manager with comprehensive monitoring."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.trading_process = None
        self.dashboard_process = None
        self.monitoring_process = None
        self.running = False
        self.session_data = {
            'start_time': None,
            'trades_executed': 0,
            'total_pnl': 0.0,
            'alerts_sent': 0,
            'errors_encountered': 0,
            'uptime_percentage': 0.0
        }
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
        
    async def start_dashboard(self) -> Optional[subprocess.Popen]:
        """Start the Streamlit dashboard."""
        try:
            logger.info("🖥️ Starting Streamlit dashboard...")
            
            dashboard_script = "phase_4_deployment/dashboard/streamlit_dashboard.py"
            if not os.path.exists(dashboard_script):
                logger.warning(f"Dashboard script not found: {dashboard_script}")
                return None
                
            process = subprocess.Popen(
                [sys.executable, "-m", "streamlit", "run", dashboard_script, "--server.headless", "true"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give dashboard time to start
            await asyncio.sleep(5)
            
            if process.poll() is None:
                logger.info(f"✅ Dashboard started successfully (PID: {process.pid})")
                logger.info("📊 Dashboard available at: http://localhost:8501")
                return process
            else:
                logger.error("❌ Dashboard failed to start")
                return None
                
        except Exception as e:
            logger.error(f"Error starting dashboard: {e}")
            return None
    
    async def start_live_trading(self) -> Optional[subprocess.Popen]:
        """Start the live trading system with signature verification fixes."""
        try:
            logger.info("🚀 Starting live trading system with signature verification fixes...")
            
            # Use the main live trading script
            trading_script = "phase_4_deployment/start_live_trading.py"
            if not os.path.exists(trading_script):
                logger.error(f"Trading script not found: {trading_script}")
                return None
            
            # Set environment variables for the session
            env = os.environ.copy()
            env["LIVE_TRADING_SESSION"] = "48_HOUR"
            env["JITO_SIGNATURE_FIXES_ENABLED"] = "true"
            env["SESSION_START_TIME"] = datetime.now().isoformat()
            
            # Start trading with enhanced components
            cmd = [
                sys.executable, 
                trading_script,
                "--use-filters",
                "--use-signal-enricher"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # Give trading system time to initialize
            await asyncio.sleep(10)
            
            if process.poll() is None:
                logger.info(f"✅ Live trading started successfully (PID: {process.pid})")
                return process
            else:
                logger.error("❌ Live trading failed to start")
                return None
                
        except Exception as e:
            logger.error(f"Error starting live trading: {e}")
            return None
    
    async def start_monitoring(self) -> Optional[subprocess.Popen]:
        """Start the real-time monitoring system."""
        try:
            logger.info("📡 Starting real-time monitoring...")
            
            monitoring_script = "phase_4_deployment/monitoring/real_time_alerts.py"
            if not os.path.exists(monitoring_script):
                logger.warning(f"Monitoring script not found: {monitoring_script}")
                return None
            
            process = subprocess.Popen(
                [sys.executable, monitoring_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give monitoring time to start
            await asyncio.sleep(3)
            
            if process.poll() is None:
                logger.info(f"✅ Monitoring started successfully (PID: {process.pid})")
                return process
            else:
                logger.warning("⚠️ Monitoring failed to start (continuing without it)")
                return None
                
        except Exception as e:
            logger.warning(f"Error starting monitoring: {e}")
            return None
    
    def check_process_health(self, process: subprocess.Popen, name: str) -> bool:
        """Check if a process is still running and healthy."""
        if process is None:
            return False
            
        if process.poll() is not None:
            logger.error(f"❌ {name} process has stopped (exit code: {process.returncode})")
            return False
            
        return True
    
    async def monitor_session(self):
        """Monitor the 48-hour trading session."""
        logger.info("🔍 Starting session monitoring...")
        
        check_interval = 60  # Check every minute
        last_status_time = time.time()
        status_interval = 300  # Status update every 5 minutes
        
        while self.running and time.time() < self.end_time.timestamp():
            try:
                current_time = time.time()
                elapsed_hours = (current_time - self.start_time.timestamp()) / 3600
                remaining_hours = 48 - elapsed_hours
                
                # Check process health
                trading_healthy = self.check_process_health(self.trading_process, "Trading")
                dashboard_healthy = self.check_process_health(self.dashboard_process, "Dashboard")
                monitoring_healthy = self.check_process_health(self.monitoring_process, "Monitoring")
                
                # Update session data
                self.session_data['uptime_percentage'] = (elapsed_hours / 48) * 100
                
                # Periodic status update
                if current_time - last_status_time >= status_interval:
                    logger.info(f"📊 SESSION STATUS - Elapsed: {elapsed_hours:.1f}h | Remaining: {remaining_hours:.1f}h")
                    logger.info(f"🔧 HEALTH - Trading: {'✅' if trading_healthy else '❌'} | "
                              f"Dashboard: {'✅' if dashboard_healthy else '❌'} | "
                              f"Monitoring: {'✅' if monitoring_healthy else '❌'}")
                    last_status_time = current_time
                
                # Restart failed processes
                if not trading_healthy and self.running:
                    logger.warning("🔄 Attempting to restart trading process...")
                    self.trading_process = await self.start_live_trading()
                    
                if not dashboard_healthy and self.running:
                    logger.warning("🔄 Attempting to restart dashboard...")
                    self.dashboard_process = await self.start_dashboard()
                
                # Save session data
                await self.save_session_data()
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"Error in session monitoring: {e}")
                await asyncio.sleep(check_interval)
    
    async def save_session_data(self):
        """Save session data to file."""
        try:
            session_file = "logs/48_hour_session_data.json"
            os.makedirs(os.path.dirname(session_file), exist_ok=True)
            
            self.session_data['last_update'] = datetime.now().isoformat()
            
            with open(session_file, 'w') as f:
                json.dump(self.session_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving session data: {e}")
    
    async def cleanup(self):
        """Clean up processes and save final data."""
        logger.info("🧹 Starting cleanup process...")
        
        processes = [
            (self.trading_process, "Trading"),
            (self.dashboard_process, "Dashboard"), 
            (self.monitoring_process, "Monitoring")
        ]
        
        for process, name in processes:
            if process and process.poll() is None:
                logger.info(f"Stopping {name} process...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                    logger.info(f"✅ {name} stopped gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ Force killing {name} process...")
                    process.kill()
        
        # Save final session data
        await self.save_session_data()
        logger.info("✅ Cleanup completed")
    
    async def run_48_hour_session(self):
        """Run the complete 48-hour live trading session."""
        try:
            self.start_time = datetime.now()
            self.end_time = self.start_time + timedelta(hours=48)
            self.running = True
            
            self.session_data['start_time'] = self.start_time.isoformat()
            
            logger.info("🚀 STARTING 48-HOUR LIVE TRADING SESSION")
            logger.info("=" * 70)
            logger.info(f"📅 Start Time: {self.start_time}")
            logger.info(f"📅 End Time: {self.end_time}")
            logger.info(f"🔧 Jito Signature Verification Fixes: ENABLED")
            logger.info("=" * 70)
            
            # Start all components
            self.dashboard_process = await self.start_dashboard()
            await asyncio.sleep(2)
            
            self.trading_process = await self.start_live_trading()
            await asyncio.sleep(2)
            
            self.monitoring_process = await self.start_monitoring()
            await asyncio.sleep(2)
            
            if not self.trading_process:
                logger.error("❌ Failed to start trading system - aborting session")
                return False
            
            logger.info("🎯 All systems started - beginning 48-hour monitoring...")
            
            # Monitor the session
            await self.monitor_session()
            
            # Session completed
            elapsed_time = datetime.now() - self.start_time
            logger.info("🎉 48-HOUR LIVE TRADING SESSION COMPLETED")
            logger.info(f"⏱️ Total Runtime: {elapsed_time}")
            logger.info(f"📊 Uptime: {self.session_data['uptime_percentage']:.1f}%")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in 48-hour session: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    """Main function to start the 48-hour live trading session."""
    
    print("🚀 SYNERGY7 48-HOUR LIVE TRADING SESSION")
    print("=" * 70)
    print("🔧 Jito Signature Verification Fixes: ENABLED")
    print("📊 Real-time Dashboard: ENABLED")
    print("📡 Monitoring & Alerts: ENABLED")
    print("⏱️ Duration: 48 Hours")
    print("=" * 70)
    
    # Confirm start
    response = input("\n🤔 Are you ready to start the 48-hour live trading session? (y/N): ")
    if response.lower() != 'y':
        print("❌ Session cancelled by user")
        return False
    
    # Create and run session
    session = LiveTrading48Hour()
    success = await session.run_48_hour_session()
    
    if success:
        print("✅ 48-hour live trading session completed successfully!")
        return True
    else:
        print("❌ 48-hour live trading session encountered errors")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
