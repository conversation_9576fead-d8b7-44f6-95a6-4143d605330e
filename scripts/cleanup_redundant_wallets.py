#!/usr/bin/env python3
"""
Cleanup Redundant Wallet & Keypair Files
Remove all wallet/keypair files that don't match the address in .env
Create a proper keypair file for the correct wallet address
"""

import os
import sys
import json
import shutil
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def print_header(message):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🧹 {message}")
    print(f"{'='*60}")

def cleanup_redundant_files():
    """Remove all redundant wallet and keypair files"""
    
    print_header("CLEANING UP REDUNDANT WALLET & KEYPAIR FILES")
    
    # Get correct wallet address from .env
    correct_wallet = os.getenv('WALLET_ADDRESS', 'J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz')
    print(f"✅ Correct wallet address: {correct_wallet}")
    
    # Files and directories to remove
    redundant_files = [
        # Empty/invalid keypair file
        'wallet/trading_wallet_keypair.json',
        
        # Encrypted wallet file (not needed)
        'keys/J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz.wallet',
        
        # Jito keypair files (not for trading)
        'keys/jito_shredstream_keypair.json',
        'keys/jito_shredstream_keypair_20250513182632.json',
        'keys/jito_shredstream_pubkey_20250513182632.txt',
        'keys/jito_auth.py',
        
        # Generated keypair file (wrong format)
        'generate_jito_keypair.py',
    ]
    
    # Directories to clean
    redundant_directories = [
        'output/live_production/trades',  # All trade files with wrong address
        'output/wallet',  # Wallet balance files
    ]
    
    removed_count = 0
    
    # Remove individual files
    print(f"\n📁 REMOVING REDUNDANT FILES:")
    for file_path in redundant_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {file_path}: {e}")
        else:
            print(f"⚪ Not found: {file_path}")
    
    # Remove directories with all contents
    print(f"\n📂 REMOVING REDUNDANT DIRECTORIES:")
    for dir_path in redundant_directories:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"✅ Removed directory: {dir_path} (and all contents)")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {dir_path}: {e}")
        else:
            print(f"⚪ Not found: {dir_path}")
    
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"   Files/directories removed: {removed_count}")
    
    return removed_count > 0

def create_proper_keypair():
    """Create a proper keypair file for the wallet address in .env"""
    
    print_header("CREATING PROPER KEYPAIR FILE")
    
    # Get wallet configuration from .env
    wallet_address = os.getenv('WALLET_ADDRESS')
    private_key_base58 = os.getenv('WALLET_PRIVATE_KEY')
    keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/trading_wallet_keypair.json')
    
    if not wallet_address or not private_key_base58:
        print("❌ Missing WALLET_ADDRESS or WALLET_PRIVATE_KEY in .env")
        return False
    
    if private_key_base58 == "***SECURELY_STORED***":
        print("❌ Please update WALLET_PRIVATE_KEY in .env with actual Base58 private key")
        return False
    
    print(f"✅ Wallet Address: {wallet_address}")
    print(f"✅ Keypair Path: {keypair_path}")
    
    try:
        # Import required modules
        from solders.keypair import Keypair
        import base58
        
        # Decode the private key
        private_key_bytes = base58.b58decode(private_key_base58)
        
        # Create keypair from private key
        keypair = Keypair.from_bytes(private_key_bytes)
        
        # Verify the public key matches
        derived_address = str(keypair.pubkey())
        if derived_address != wallet_address:
            print(f"❌ Address mismatch!")
            print(f"   Expected: {wallet_address}")
            print(f"   Derived:  {derived_address}")
            return False
        
        # Convert keypair to JSON format (array of bytes)
        keypair_bytes = bytes(keypair)
        keypair_data = list(keypair_bytes)
        
        # Create directory if needed
        os.makedirs(os.path.dirname(keypair_path), exist_ok=True)
        
        # Save keypair file
        with open(keypair_path, 'w') as f:
            json.dump(keypair_data, f)
        
        # Set secure permissions
        os.chmod(keypair_path, 0o600)
        
        print(f"✅ Created keypair file: {keypair_path}")
        print(f"✅ Address verification: PASSED")
        print(f"✅ File permissions: 600 (secure)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating keypair: {e}")
        return False

def update_wallet_config():
    """Update wallet configuration files"""
    
    print_header("UPDATING WALLET CONFIGURATION")
    
    wallet_address = os.getenv('WALLET_ADDRESS')
    
    # Update keys/wallet_config.json
    config_file = 'keys/wallet_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            config['wallet_address'] = wallet_address
            
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"✅ Updated: {config_file}")
            
        except Exception as e:
            print(f"❌ Failed to update {config_file}: {e}")
    
    # Create fresh output directories
    output_dirs = [
        'output/live_production/trades',
        'output/wallet',
        'output/metrics'
    ]
    
    for dir_path in output_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✅ Created: {dir_path}")

def create_backup():
    """Create backup of important files before cleanup"""
    
    print_header("CREATING BACKUP")
    
    backup_dir = f"backups/wallet_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Backup important files
    backup_files = [
        '.env',
        'keys/wallet_config.json',
    ]
    
    for file_path in backup_files:
        if os.path.exists(file_path):
            try:
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
                print(f"✅ Backed up: {file_path} -> {backup_path}")
            except Exception as e:
                print(f"❌ Backup failed for {file_path}: {e}")
    
    print(f"✅ Backup created: {backup_dir}")
    return backup_dir

def main():
    """Main cleanup function"""
    
    print("🧹 WALLET & KEYPAIR CLEANUP UTILITY")
    print("=" * 60)
    print("⚠️  This will remove ALL redundant wallet/keypair files")
    print("✅ Only files matching your .env configuration will remain")
    print("=" * 60)
    
    # Get current configuration
    wallet_address = os.getenv('WALLET_ADDRESS')
    if not wallet_address:
        print("❌ No WALLET_ADDRESS found in .env file")
        return False
    
    print(f"🎯 Target wallet: {wallet_address}")
    
    # Create backup first
    backup_dir = create_backup()
    
    # Step 1: Cleanup redundant files
    cleanup_success = cleanup_redundant_files()
    
    # Step 2: Create proper keypair
    keypair_success = create_proper_keypair()
    
    # Step 3: Update configuration
    update_wallet_config()
    
    # Summary
    print_header("CLEANUP COMPLETE")
    
    if cleanup_success and keypair_success:
        print("🎉 SUCCESS: Wallet cleanup completed successfully!")
        print(f"✅ Redundant files removed")
        print(f"✅ Proper keypair created")
        print(f"✅ Configuration updated")
        print(f"✅ Backup saved: {backup_dir}")
        print(f"\n🚀 System ready for trading with wallet: {wallet_address}")
        return True
    else:
        print("❌ PARTIAL SUCCESS: Some issues occurred")
        print(f"⚠️ Check the output above for details")
        print(f"💾 Backup available: {backup_dir}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
