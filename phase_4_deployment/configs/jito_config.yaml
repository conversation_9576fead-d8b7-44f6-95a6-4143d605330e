# Jito Configuration for Live Trading
# Configuration for Jito Block Engine integration with signature verification fixes

# Jito Block Engine Configuration
jito:
  # Primary Jito endpoints (with signature verification fixes)
  block_engine_url: "https://mainnet.block-engine.jito.wtf"
  relayer_url: "http://amsterdam.mainnet.relayer.jito.wtf:8100"
  
  # Regional endpoints for failover
  endpoints:
    amsterdam:
      block_engine: "https://amsterdam.mainnet.block-engine.jito.wtf"
      relayer: "http://amsterdam.mainnet.relayer.jito.wtf:8100"
      shred_receiver: "74.118.140.240:1002"
    frankfurt:
      block_engine: "https://frankfurt.mainnet.block-engine.jito.wtf"
      relayer: "http://frankfurt.mainnet.relayer.jito.wtf:8100"
      shred_receiver: "64.130.50.14:1002"
    new_york:
      block_engine: "https://ny.mainnet.block-engine.jito.wtf"
      relayer: "http://ny.mainnet.relayer.jito.wtf:8100"
      shred_receiver: "141.98.216.96:1002"
    tokyo:
      block_engine: "https://tokyo.mainnet.block-engine.jito.wtf"
      relayer: "http://tokyo.mainnet.relayer.jito.wtf:8100"
      shred_receiver: "202.8.9.160:1002"

# Transaction Configuration (with signature verification fixes)
transaction:
  # Encoding settings (FIXED: Use base64 as recommended by Jito)
  encoding: "base64"  # base64 recommended over deprecated base58
  
  # Blockhash management (FIXED: Fresh blockhash timing)
  fresh_blockhash_enabled: true
  blockhash_cache_duration: 30  # seconds
  max_blockhash_age: 150  # slots (~60-90 seconds)
  
  # Transaction validation (FIXED: Serialization validation)
  validate_serialization: true
  validate_base64_encoding: true
  
  # Retry configuration
  max_retries: 3
  retry_delay: 1.0
  timeout: 30
  skip_preflight: true
  
  # Default tip configuration
  default_tip: 1000  # lamports (minimum required by Jito)
  tip_multiplier: 1.5  # Increase tip during high demand

# Bundle Configuration (with signature verification fixes)
bundle:
  # Bundle format (FIXED: Correct JSON-RPC format)
  max_transactions: 5  # Maximum transactions per bundle
  encoding: "base64"  # FIXED: Explicit base64 encoding
  
  # Bundle validation
  validate_bundle_format: true
  validate_transaction_encoding: true
  
  # Tip configuration for bundles
  min_tip_lamports: 1000
  tip_accounts:
    - "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5"
    - "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe"
    - "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"
    - "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49"
    - "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh"
    - "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt"
    - "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL"
    - "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"

# Rate Limiting
rate_limiting:
  requests_per_second: 1  # Default Jito rate limit
  burst_limit: 5
  backoff_multiplier: 2.0
  max_backoff: 30.0

# Monitoring and Logging
monitoring:
  enabled: true
  log_level: "INFO"
  metrics_enabled: true
  metrics_path: "output/jito_metrics.json"
  verbose_logging: false
  
  # Health checks
  health_check_interval: 60  # seconds
  endpoint_health_timeout: 10  # seconds

# Circuit Breaker
circuit_breaker:
  enabled: true
  failure_threshold: 5
  recovery_timeout: 300  # seconds
  half_open_max_calls: 3

# Signature Verification Fixes (ENABLED)
signature_verification_fixes:
  enabled: true
  fresh_blockhash_timing: true
  transaction_encoding_validation: true
  bundle_format_compliance: true
  base64_encoding_validation: true
  
  # Fix validation
  validate_fixes_on_startup: true
  log_fix_status: true

# Development and Testing
development:
  dry_run: false
  simulation_mode: false
  test_mode: false
  debug_logging: false
  
  # Test configuration
  test_bundle_size: 1
  test_tip_amount: 1000
  test_timeout: 30

# Fallback Configuration
fallback:
  enabled: true
  fallback_to_helius: true
  fallback_timeout: 5.0
  max_fallback_attempts: 3

# Performance Tuning
performance:
  connection_pool_size: 10
  keep_alive_timeout: 30
  read_timeout: 30
  connect_timeout: 10
  
  # Batch processing
  batch_enabled: false
  batch_size: 3
  batch_timeout: 5.0

# Security
security:
  validate_responses: true
  check_ssl_certificates: true
  max_response_size: 1048576  # 1MB
  
  # API key management (if needed in future)
  api_key_rotation_enabled: false
  api_key_rotation_interval: 86400  # 24 hours
