#!/bin/bash
# Run load test for Synergy7 Trading System

# Set default values
CONFIG_FILE="config.yaml"
OUTPUT_DIR="output/load_tests"
DURATION=300
TPS=10
RAMP_UP=60

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --config)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --output)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --duration)
      DURATION="$2"
      shift 2
      ;;
    --tps)
      TPS="$2"
      shift 2
      ;;
    --ramp-up)
      RAMP_UP="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --config FILE     Path to configuration file (default: config.yaml)"
      echo "  --output DIR      Directory to store test results (default: output/load_tests)"
      echo "  --duration SEC    Test duration in seconds (default: 300)"
      echo "  --tps NUM         Target transactions per second (default: 10)"
      echo "  --ramp-up SEC     Ramp-up period in seconds (default: 60)"
      echo "  --help            Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Make sure the script is executable
chmod +x phase_4_deployment/scripts/load_test.py

# Run the load test
echo "Running load test with config: $CONFIG_FILE, output: $OUTPUT_DIR, duration: $DURATION seconds, TPS: $TPS, ramp-up: $RAMP_UP seconds"
python phase_4_deployment/scripts/load_test.py --config "$CONFIG_FILE" --output "$OUTPUT_DIR" --duration "$DURATION" --tps "$TPS" --ramp-up "$RAMP_UP"

# Check exit code
if [ $? -eq 0 ]; then
  echo "Load test completed successfully"
  exit 0
else
  echo "Load test failed"
  exit 1
fi
