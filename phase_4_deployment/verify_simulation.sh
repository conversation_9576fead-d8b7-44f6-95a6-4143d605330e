#!/bin/bash
# Verify simulation results for Synergy7 Trading System

# Check if results file is provided
if [ $# -lt 1 ]; then
  echo "Usage: $0 RESULTS_FILE [--config CONFIG_FILE] [--output OUTPUT_FILE]"
  echo "Example: $0 output/simulation_tests/simulation_results_20230101_120000.json"
  exit 1
fi

RESULTS_FILE="$1"
shift

# Parse additional arguments
CONFIG_FILE="config.yaml"
OUTPUT_FILE=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --config)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --output)
      OUTPUT_FILE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 RESULTS_FILE [options]"
      echo "Options:"
      echo "  --config FILE     Path to configuration file (default: config.yaml)"
      echo "  --output FILE     Path to output verification report"
      echo "  --help            Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Make sure the script is executable
chmod +x phase_4_deployment/scripts/verify_simulation.py

# Run the verification script
echo "Verifying simulation results: $RESULTS_FILE"
if [ -n "$OUTPUT_FILE" ]; then
  python phase_4_deployment/scripts/verify_simulation.py "$RESULTS_FILE" --config "$CONFIG_FILE" --output "$OUTPUT_FILE"
else
  python phase_4_deployment/scripts/verify_simulation.py "$RESULTS_FILE" --config "$CONFIG_FILE"
fi

# Check exit code
if [ $? -eq 0 ]; then
  echo "Verification passed"
  exit 0
else
  echo "Verification failed"
  exit 1
fi
