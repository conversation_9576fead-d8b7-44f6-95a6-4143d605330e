"""
Enhanced Jupiter Builder with Immediate Blockhash Handling
"""


async def build_jupiter_swap_immediate(self, signal: Dict[str, Any]) -> Optional[bytes]:
    """
    Build Jupiter swap with IMMEDIATE blockhash to prevent timing issues.
    This is the CRITICAL FIX for blockhash expiration.
    """
    try:
        # STEP 1: Get fresh blockhash IMMEDIATELY
        import httpx
        from solders.hash import Hash
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            payload = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'getLatestBlockhash',
                'params': [{'commitment': 'confirmed'}]
            }
            
            rpc_url = f'https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}'
            response = await client.post(rpc_url, json=payload)
            result = response.json()
            
            if 'result' in result and 'value' in result['result']:
                blockhash_str = result['result']['value']['blockhash']
                fresh_blockhash = Hash.from_string(blockhash_str)
                logger.info(f"Got IMMEDIATE fresh blockhash: {blockhash_str}")
            else:
                logger.error("Failed to get immediate blockhash")
                return None
        
        # STEP 2: Get Jupiter quote with fresh data
        quote = await self._get_jupiter_quote_immediate(signal)
        if not quote:
            logger.error("Failed to get Jupiter quote")
            return None
            
        # STEP 3: Get Jupiter transaction with immediate execution
        jupiter_tx_data = await self._get_jupiter_swap_immediate(quote, fresh_blockhash)
        if not jupiter_tx_data:
            logger.error("Failed to get Jupiter transaction")
            return None
            
        # STEP 4: Sign immediately with fresh blockhash
        signed_tx = await self._sign_jupiter_immediate(jupiter_tx_data, fresh_blockhash)
        if not signed_tx:
            logger.error("Failed to sign Jupiter transaction")
            return None
            
        logger.info("✅ Jupiter transaction built with immediate blockhash handling")
        return signed_tx
        
    except Exception as e:
        logger.error(f"Error in immediate Jupiter build: {e}")
        return None
