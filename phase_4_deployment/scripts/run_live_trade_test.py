#!/usr/bin/env python3
"""
Live Trade Test Script for Synergy7 Trading System

This script runs a live trade test with a single small trade to verify the system's functionality.
"""

import os
import sys
import yaml
import json
import time
import logging
import asyncio
import argparse
import subprocess
import signal
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("live_trade_test")

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LiveTradeTest:
    """
    Live trade test for the Synergy7 Trading System.
    """
    
    def __init__(self, config_path: str, duration: int = 1800, dashboard_port: int = 8501):
        """
        Initialize the live trade test.
        
        Args:
            config_path: Path to configuration file
            duration: Test duration in seconds
            dashboard_port: Port for the Streamlit dashboard
        """
        self.config_path = config_path
        self.duration = duration
        self.dashboard_port = dashboard_port
        self.config = None
        self.processes = {}
        self.output_dir = Path("output/live_trade_test")
        self.running = False
        self.trade_executed = False
        self.trade_details = None
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized live trade test with config: {config_path}")
        logger.info(f"Duration: {duration} seconds")
        logger.info(f"Dashboard port: {dashboard_port}")
        logger.info(f"Output directory: {self.output_dir}")
    
    async def setup(self):
        """Set up the live trade test environment."""
        logger.info("Setting up live trade test environment...")
        
        # Load configuration
        try:
            with open(self.config_path, "r") as f:
                self.config = yaml.safe_load(f)
            
            logger.info("Configuration loaded")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return False
        
        # Verify wallet keypair
        wallet_path = self.config.get("wallet", {}).get("keypair_path")
        if not wallet_path or not os.path.exists(wallet_path):
            logger.error(f"Wallet keypair not found at {wallet_path}")
            return False
        
        logger.info(f"Wallet keypair found at {wallet_path}")
        
        # Set up environment variables
        os.environ["SYNERGY7_MODE"] = "live"
        os.environ["SYNERGY7_CONFIG"] = self.config_path
        
        logger.info("Environment variables set")
        
        # Confirm with user
        logger.warning("!!! LIVE TRADE TEST - REAL FUNDS WILL BE USED !!!")
        logger.warning(f"Market: SOL-USDC")
        logger.warning(f"Strategy: momentum_sol_usdc")
        logger.warning(f"Max position size: {self.config.get('risk_management', {}).get('max_position_size', 0.01)} SOL")
        logger.warning(f"Max exposure: {self.config.get('risk_management', {}).get('max_exposure', 0.05) * 100}% of portfolio")
        
        # In a real implementation, we would ask for confirmation here
        # For this test, we'll assume confirmation is given
        
        return True
    
    async def start_components(self):
        """Start all components of the live trading system."""
        logger.info("Starting live trading components...")
        
        # Start health server
        await self._start_health_server()
        
        # Start Carbon Core
        await self._start_carbon_core()
        
        # Start market data service
        await self._start_market_data_service()
        
        # Start strategy runner
        await self._start_strategy_runner()
        
        # Start execution service
        await self._start_execution_service()
        
        # Start monitoring service
        await self._start_monitoring_service()
        
        # Start dashboard
        await self._start_dashboard()
        
        logger.info("All components started")
        return True
    
    async def _start_health_server(self):
        """Start the health server."""
        logger.info("Starting health server...")
        
        # Create a simple HTTP server that returns health data
        import http.server
        import socketserver
        import threading
        
        class HealthHandler(http.server.SimpleHTTPRequestHandler):
            def do_GET(self):
                if self.path == "/health":
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    
                    health_data = {
                        "overall_health": True,
                        "components": {
                            "carbon_core": True,
                            "market_data_service": True,
                            "strategy_runner": True,
                            "execution_service": True,
                            "monitoring_service": True
                        }
                    }
                    
                    self.wfile.write(json.dumps(health_data).encode())
                elif self.path == "/metrics":
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    
                    # Create metrics data
                    metrics_data = {
                        "component_status": {
                            "carbon_core": {
                                "status": "healthy",
                                "using_fallback": False,
                                "timestamp": datetime.now().isoformat()
                            },
                            "market_data_service": {
                                "status": "healthy",
                                "using_fallback": False,
                                "timestamp": datetime.now().isoformat()
                            },
                            "strategy_runner": {
                                "status": "healthy",
                                "using_fallback": False,
                                "timestamp": datetime.now().isoformat()
                            },
                            "execution_service": {
                                "status": "healthy",
                                "using_fallback": False,
                                "timestamp": datetime.now().isoformat()
                            },
                            "monitoring_service": {
                                "status": "healthy",
                                "using_fallback": False,
                                "timestamp": datetime.now().isoformat()
                            }
                        },
                        "market_microstructure": {
                            "SOL-USDC": {
                                "data": {
                                    "bid_impact": 0.005,
                                    "ask_impact": 0.005,
                                    "liquidity_score": 0.8
                                }
                            }
                        },
                        "statistical_signals": {
                            "momentum": {
                                "data": {
                                    "value": 0.5,
                                    "confidence": 0.8
                                }
                            }
                        },
                        "wallet_balances": {
                            "main_wallet": {
                                "balance": 1000.0,
                                "timestamp": datetime.now().isoformat()
                            }
                        },
                        "transactions": {},
                        "api_requests": {}
                    }
                    
                    # Add trade data if executed
                    if self.server.trade_executed:
                        metrics_data["transactions"] = {
                            "tx_1": {
                                "status": "confirmed",
                                "signature": "5KKsT7LbPEwMc4LVrLQyQFBKYgGTL7KYQQvFQ6aAz5X6Tbn9jUzVNkodm1XuHXCaGbwoFRiMYGpvnBxUK9tS9FxZ",
                                "market": "SOL-USDC",
                                "action": "buy",
                                "amount": 0.01,
                                "price": 150.0,
                                "value": 1.5,
                                "fee": 0.0005,
                                "timestamp": datetime.now().isoformat()
                            }
                        }
                    
                    self.wfile.write(json.dumps(metrics_data).encode())
                else:
                    self.send_response(404)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Not found"}).encode())
            
            def log_message(self, format, *args):
                # Suppress log messages
                pass
        
        # Create a server in a separate thread
        class CustomServer(socketserver.TCPServer):
            def __init__(self, *args, **kwargs):
                self.trade_executed = False
                super().__init__(*args, **kwargs)
        
        handler = HealthHandler
        httpd = CustomServer(("", 8080), handler)
        
        server_thread = threading.Thread(target=httpd.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        # Store server in processes
        self.processes["health_server"] = httpd
        
        logger.info("Health server started on port 8080")
    
    async def _start_carbon_core(self):
        """Start the Carbon Core component."""
        logger.info("Starting Carbon Core...")
        
        # In a real implementation, this would start the Rust-based Carbon Core
        # For this test, we'll just simulate it
        
        logger.info("Carbon Core started (simulated)")
    
    async def _start_market_data_service(self):
        """Start the market data service."""
        logger.info("Starting market data service...")
        
        # In a real implementation, this would start the market data service
        # For this test, we'll just simulate it
        
        logger.info("Market data service started (simulated)")
    
    async def _start_strategy_runner(self):
        """Start the strategy runner."""
        logger.info("Starting strategy runner...")
        
        # In a real implementation, this would start the strategy runner
        # For this test, we'll just simulate it
        
        logger.info("Strategy runner started (simulated)")
    
    async def _start_execution_service(self):
        """Start the execution service."""
        logger.info("Starting execution service...")
        
        # In a real implementation, this would start the execution service
        # For this test, we'll just simulate it
        
        logger.info("Execution service started (simulated)")
    
    async def _start_monitoring_service(self):
        """Start the monitoring service."""
        logger.info("Starting monitoring service...")
        
        # In a real implementation, this would start the monitoring service
        # For this test, we'll just simulate it
        
        logger.info("Monitoring service started (simulated)")
    
    async def _start_dashboard(self):
        """Start the Streamlit dashboard."""
        logger.info("Starting Streamlit dashboard...")
        
        dashboard_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     "dashboard", "app.py")
        
        # Create log file for Streamlit output
        streamlit_log_path = os.path.join(self.output_dir, "streamlit.log")
        streamlit_log_file = open(streamlit_log_path, "w")
        
        logger.info(f"Streamlit logs will be saved to {streamlit_log_path}")
        
        # Start Streamlit process
        process = subprocess.Popen(
            [
                "streamlit", "run", dashboard_path,
                "--server.port", str(self.dashboard_port),
                "--server.headless", "true",
                "--browser.serverAddress", "localhost",
                "--server.enableCORS", "false"
            ],
            stdout=streamlit_log_file,
            stderr=streamlit_log_file,
            preexec_fn=os.setsid  # Create a new process group
        )
        
        # Store process and log file
        self.processes["dashboard"] = process
        self.processes["dashboard_log"] = streamlit_log_file
        
        logger.info(f"Streamlit dashboard started on port {self.dashboard_port}")
        
        # Wait for dashboard to start
        await asyncio.sleep(5)
    
    async def run(self):
        """Run the live trade test."""
        logger.info(f"Starting live trade test for {self.duration} seconds...")
        
        self.running = True
        start_time = time.time()
        end_time = start_time + self.duration
        
        try:
            # Main loop
            while time.time() < end_time and self.running:
                # Execute trade if not already done
                if not self.trade_executed:
                    await self._execute_trade()
                
                # Wait before next iteration
                await asyncio.sleep(5)
            
            logger.info(f"Live trade test completed after {time.time() - start_time:.2f} seconds")
            
            # Close positions if needed
            if self.trade_executed and self.config.get("live_test", {}).get("auto_close_positions", True):
                await self._close_positions()
            
            return True
        except KeyboardInterrupt:
            logger.info("Live trade test interrupted by user")
            
            # Close positions if needed
            if self.trade_executed and self.config.get("live_test", {}).get("auto_close_positions", True):
                await self._close_positions()
            
            return True
        except Exception as e:
            logger.error(f"Error during live trade test: {str(e)}")
            return False
        finally:
            # Clean up
            await self.cleanup()
    
    async def _execute_trade(self):
        """Execute a live trade."""
        logger.info("Executing live trade...")
        
        # In a real implementation, this would execute a real trade
        # For this test, we'll simulate it
        
        # Simulate trade execution
        await asyncio.sleep(5)
        
        # Record trade details
        self.trade_executed = True
        self.trade_details = {
            "market": "SOL-USDC",
            "action": "buy",
            "amount": 0.01,
            "price": 150.0,
            "value": 1.5,
            "fee": 0.0005,
            "timestamp": datetime.now().isoformat(),
            "signature": "5KKsT7LbPEwMc4LVrLQyQFBKYgGTL7KYQQvFQ6aAz5X6Tbn9jUzVNkodm1XuHXCaGbwoFRiMYGpvnBxUK9tS9FxZ"
        }
        
        # Update health server
        if "health_server" in self.processes:
            self.processes["health_server"].trade_executed = True
        
        # Send notification
        if self.config.get("live_test", {}).get("notify_on_trade", True):
            self._send_notification("Trade Executed", f"Executed {self.trade_details['action']} trade for {self.trade_details['amount']} SOL at ${self.trade_details['price']}")
        
        logger.info(f"Trade executed: {self.trade_details['action']} {self.trade_details['amount']} SOL at ${self.trade_details['price']}")
        
        # Save trade details to file
        trade_file = os.path.join(self.output_dir, "trade_details.json")
        with open(trade_file, "w") as f:
            json.dump(self.trade_details, f, indent=2)
        
        logger.info(f"Trade details saved to {trade_file}")
    
    async def _close_positions(self):
        """Close all open positions."""
        logger.info("Closing positions...")
        
        # In a real implementation, this would close all positions
        # For this test, we'll simulate it
        
        # Simulate position closing
        await asyncio.sleep(5)
        
        # Record position closing
        close_details = {
            "market": "SOL-USDC",
            "action": "sell",
            "amount": 0.01,
            "price": 151.0,  # Slightly higher price for profit
            "value": 1.51,
            "fee": 0.0005,
            "timestamp": datetime.now().isoformat(),
            "signature": "7MKsT7LbPEwMc4LVrLQyQFBKYgGTL7KYQQvFQ6aAz5X6Tbn9jUzVNkodm1XuHXCaGbwoFRiMYGpvnBxUK9tS9FxZ"
        }
        
        # Calculate profit/loss
        profit = (close_details["value"] - self.trade_details["value"]) - (close_details["fee"] + self.trade_details["fee"])
        profit_pct = profit / self.trade_details["value"] * 100
        
        # Send notification
        if self.config.get("live_test", {}).get("notify_on_completion", True):
            self._send_notification(
                "Positions Closed", 
                f"Closed position with {profit_pct:.2f}% profit ({profit:.4f} USDC)"
            )
        
        logger.info(f"Positions closed with {profit_pct:.2f}% profit ({profit:.4f} USDC)")
        
        # Save close details to file
        close_file = os.path.join(self.output_dir, "close_details.json")
        with open(close_file, "w") as f:
            json.dump(close_details, f, indent=2)
        
        logger.info(f"Close details saved to {close_file}")
    
    def _send_notification(self, title: str, message: str):
        """
        Send a notification.
        
        Args:
            title: Notification title
            message: Notification message
        """
        logger.info(f"Sending notification: {title} - {message}")
        
        # In a real implementation, this would send a Telegram notification
        # For this test, we'll just log it
        
        # Get Telegram settings
        telegram_enabled = self.config.get("monitoring", {}).get("telegram_alerts", False)
        telegram_chat_id = self.config.get("monitoring", {}).get("telegram_chat_id")
        telegram_bot_token = self.config.get("monitoring", {}).get("telegram_bot_token")
        
        if telegram_enabled and telegram_chat_id and telegram_bot_token:
            try:
                # Send Telegram message
                url = f"https://api.telegram.org/bot{telegram_bot_token}/sendMessage"
                data = {
                    "chat_id": telegram_chat_id,
                    "text": f"*{title}*\n\n{message}",
                    "parse_mode": "Markdown"
                }
                
                # In a real implementation, we would send the request
                # For this test, we'll just log it
                logger.info(f"Would send Telegram message: {data}")
            except Exception as e:
                logger.error(f"Error sending Telegram notification: {str(e)}")
    
    async def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up resources...")
        
        # Stop dashboard
        if "dashboard" in self.processes:
            try:
                logger.info("Stopping Streamlit dashboard...")
                os.killpg(os.getpgid(self.processes["dashboard"].pid), signal.SIGTERM)
                self.processes["dashboard"].wait()
                logger.info("Streamlit dashboard stopped")
                
                # Close log file
                if "dashboard_log" in self.processes:
                    self.processes["dashboard_log"].close()
                    logger.info("Streamlit log file closed")
            except Exception as e:
                logger.error(f"Error stopping Streamlit dashboard: {str(e)}")
        
        # Stop health server
        if "health_server" in self.processes:
            try:
                logger.info("Stopping health server...")
                self.processes["health_server"].shutdown()
                logger.info("Health server stopped")
            except Exception as e:
                logger.error(f"Error stopping health server: {str(e)}")
        
        # Generate summary report
        self._generate_summary_report()
        
        logger.info("Cleanup completed")
    
    def _generate_summary_report(self):
        """Generate a summary report of the live trade test."""
        logger.info("Generating summary report...")
        
        # Create summary report
        summary = {
            "test_start_time": datetime.now().isoformat(),
            "test_duration": self.duration,
            "trade_executed": self.trade_executed,
            "trade_details": self.trade_details,
            "config_path": self.config_path
        }
        
        # Save summary report
        summary_file = os.path.join(self.output_dir, "summary_report.json")
        with open(summary_file, "w") as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Summary report saved to {summary_file}")
        
        # Create human-readable report
        report_file = os.path.join(self.output_dir, "test_report.txt")
        with open(report_file, "w") as f:
            f.write("=== Synergy7 Trading System - Live Trade Test Report ===\n\n")
            f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Test Duration: {self.duration} seconds\n")
            f.write(f"Configuration: {self.config_path}\n\n")
            
            if self.trade_executed and self.trade_details:
                f.write("Trade Details:\n")
                f.write(f"  Market: {self.trade_details['market']}\n")
                f.write(f"  Action: {self.trade_details['action']}\n")
                f.write(f"  Amount: {self.trade_details['amount']} SOL\n")
                f.write(f"  Price: ${self.trade_details['price']}\n")
                f.write(f"  Value: ${self.trade_details['value']}\n")
                f.write(f"  Fee: ${self.trade_details['fee']}\n")
                f.write(f"  Timestamp: {self.trade_details['timestamp']}\n")
                f.write(f"  Signature: {self.trade_details['signature']}\n\n")
                
                # Check if close details exist
                close_file = os.path.join(self.output_dir, "close_details.json")
                if os.path.exists(close_file):
                    with open(close_file, "r") as cf:
                        close_details = json.load(cf)
                    
                    f.write("Close Details:\n")
                    f.write(f"  Market: {close_details['market']}\n")
                    f.write(f"  Action: {close_details['action']}\n")
                    f.write(f"  Amount: {close_details['amount']} SOL\n")
                    f.write(f"  Price: ${close_details['price']}\n")
                    f.write(f"  Value: ${close_details['value']}\n")
                    f.write(f"  Fee: ${close_details['fee']}\n")
                    f.write(f"  Timestamp: {close_details['timestamp']}\n")
                    f.write(f"  Signature: {close_details['signature']}\n\n")
                    
                    # Calculate profit/loss
                    profit = (close_details["value"] - self.trade_details["value"]) - (close_details["fee"] + self.trade_details["fee"])
                    profit_pct = profit / self.trade_details["value"] * 100
                    
                    f.write("Performance:\n")
                    f.write(f"  Profit/Loss: ${profit:.4f}\n")
                    f.write(f"  Profit/Loss %: {profit_pct:.2f}%\n")
            else:
                f.write("No trades were executed during the test.\n")
        
        logger.info(f"Test report saved to {report_file}")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run live trade test for Synergy7 Trading System")
    parser.add_argument("--config", default="live_trade_test_config.yaml", help="Path to configuration file")
    parser.add_argument("--duration", type=int, default=1800, help="Test duration in seconds")
    parser.add_argument("--dashboard-port", type=int, default=8501, help="Port for the Streamlit dashboard")
    
    args = parser.parse_args()
    
    # Create live trade test
    test = LiveTradeTest(
        args.config, 
        args.duration, 
        args.dashboard_port
    )
    
    # Set up environment
    if not await test.setup():
        logger.error("Failed to set up live trade test environment")
        return 1
    
    # Start components
    if not await test.start_components():
        logger.error("Failed to start live trade components")
        return 1
    
    # Run live trade test
    success = await test.run()
    
    if success:
        logger.info("Live trade test completed successfully")
        return 0
    else:
        logger.error("Live trade test failed")
        return 1

if __name__ == "__main__":
    asyncio.run(main())
