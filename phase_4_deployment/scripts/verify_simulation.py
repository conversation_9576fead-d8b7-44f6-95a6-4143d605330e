#!/usr/bin/env python3
"""
Verification Script for Synergy7 Trading System

This script verifies the results of a simulation test and checks if the system
is ready for production deployment.
"""

import os
import sys
import json
import yaml
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("verification")

# ANSI color codes
GREEN = '\033[0;32m'
YELLOW = '\033[1;33m'
RED = '\033[0;31m'
BLUE = '\033[0;34m'
NC = '\033[0m'  # No Color

class SimulationVerifier:
    """
    Verifies the results of a simulation test.
    """

    def __init__(self, results_file: str, config_path: str = "config.yaml"):
        """
        Initialize the verifier.

        Args:
            results_file: Path to simulation results file
            config_path: Path to configuration file
        """
        self.results_file = Path(results_file)
        self.config_path = Path(config_path)
        self.results = None
        self.config = None
        self.verification_results = {
            "timestamp": datetime.now().isoformat(),
            "results_file": str(self.results_file),
            "config_file": str(self.config_path),
            "checks": [],
            "overall_status": "FAILED",
            "score": 0,
            "max_score": 0,
            "recommendations": []
        }

        logger.info(f"Initialized verifier with results: {results_file}, config: {config_path}")

    def load_data(self) -> bool:
        """
        Load simulation results and configuration.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Load simulation results
            if not self.results_file.exists():
                logger.error(f"Results file not found: {self.results_file}")
                # Create a minimal results structure for verification
                self.results = {
                    "start_time": datetime.now().isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration": 0,
                    "components": {},
                    "metrics": {},
                    "errors": ["Results file not found"],
                    "success": False
                }
                logger.warning("Created minimal results structure for verification")
            else:
                with open(self.results_file, "r") as f:
                    self.results = json.load(f)

            # Load configuration
            if not self.config_path.exists():
                logger.error(f"Configuration file not found: {self.config_path}")
                # Create a minimal config structure
                self.config = {
                    "simulation": {"duration": 300},
                    "mode": {"simulation": True}
                }
                logger.warning("Created minimal configuration for verification")
            else:
                with open(self.config_path, "r") as f:
                    self.config = yaml.safe_load(f)

            logger.info("Data loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            # Create minimal structures for verification
            self.results = {
                "start_time": datetime.now().isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": 0,
                "components": {},
                "metrics": {},
                "errors": [f"Error loading data: {str(e)}"],
                "success": False
            }
            self.config = {
                "simulation": {"duration": 300},
                "mode": {"simulation": True}
            }
            logger.warning("Created minimal structures for verification after error")
            return True  # Return True to continue verification with minimal data

    def verify(self) -> bool:
        """
        Verify the simulation results.

        Returns:
            True if verification passed, False otherwise
        """
        if not self.results or not self.config:
            logger.error("No data to verify")
            return False

        # Verify simulation success
        self._add_check(
            "Simulation Completed",
            "Simulation test completed successfully",
            self.results.get("success", False),
            10
        )

        # Verify component status
        components = self.results.get("components", {})
        all_components_healthy = True

        for name, status in components.items():
            component_status = status.get("status", "unknown")
            is_healthy = component_status != "unhealthy"

            if not is_healthy:
                all_components_healthy = False

            self._add_check(
                f"Component: {name}",
                f"Component {name} is healthy",
                is_healthy,
                5
            )

        # Verify error count
        errors = self.results.get("errors", [])
        error_count = len(errors)

        self._add_check(
            "Error Count",
            f"Simulation had {error_count} errors",
            error_count == 0,
            10
        )

        # Verify metrics
        metrics = self.results.get("metrics", {})

        # Check transaction metrics if available
        if "transaction_executor" in metrics:
            tx_metrics = metrics["transaction_executor"]

            # Check successful transactions
            successful_tx = tx_metrics.get("successful_transactions", 0)
            failed_tx = tx_metrics.get("failed_transactions", 0)
            total_tx = tx_metrics.get("total_transactions", 0)

            if total_tx > 0:
                success_rate = successful_tx / total_tx

                self._add_check(
                    "Transaction Success Rate",
                    f"Transaction success rate: {success_rate:.2%}",
                    success_rate >= 0.95,
                    10
                )

            # Check circuit breaker trips
            circuit_breaker_trips = tx_metrics.get("circuit_breaker_trips", 0)

            self._add_check(
                "Circuit Breaker Trips",
                f"Circuit breaker tripped {circuit_breaker_trips} times",
                circuit_breaker_trips == 0,
                5
            )

        # Check API metrics if available
        if "api_manager" in metrics:
            api_metrics = metrics["api_manager"]

            # Check API success rate
            api_success = api_metrics.get("successful_calls", 0)
            api_failed = api_metrics.get("failed_calls", 0)
            api_total = api_success + api_failed

            if api_total > 0:
                api_success_rate = api_success / api_total

                self._add_check(
                    "API Success Rate",
                    f"API success rate: {api_success_rate:.2%}",
                    api_success_rate >= 0.95,
                    10
                )

        # Check duration
        duration = self.results.get("duration", 0)
        expected_duration = self.config.get("simulation", {}).get("duration", 300)

        self._add_check(
            "Simulation Duration",
            f"Simulation ran for {duration:.2f} seconds (expected: {expected_duration})",
            abs(duration - expected_duration) < 30,  # Allow 30 seconds deviation
            5
        )

        # Calculate overall score
        score, max_score = self._calculate_score()
        score_pct = (score / max_score) * 100 if max_score > 0 else 0

        self.verification_results["score"] = score
        self.verification_results["max_score"] = max_score

        # Determine overall status
        if score_pct >= 80:
            self.verification_results["overall_status"] = "PASSED"
        elif score_pct >= 60:
            self.verification_results["overall_status"] = "WARNING"
        else:
            self.verification_results["overall_status"] = "FAILED"

        # Add recommendations
        self._add_recommendations()

        logger.info(f"Verification completed with status: {self.verification_results['overall_status']}")
        return self.verification_results["overall_status"] == "PASSED"

    def _add_check(self, name: str, description: str, passed: bool, points: int) -> None:
        """
        Add a verification check.

        Args:
            name: Check name
            description: Check description
            passed: Whether the check passed
            points: Points for the check
        """
        self.verification_results["checks"].append({
            "name": name,
            "description": description,
            "passed": passed,
            "points": points
        })

    def _calculate_score(self) -> Tuple[int, int]:
        """
        Calculate the verification score.

        Returns:
            Tuple of (score, max_score)
        """
        score = 0
        max_score = 0

        for check in self.verification_results["checks"]:
            max_score += check["points"]
            if check["passed"]:
                score += check["points"]

        return score, max_score

    def _add_recommendations(self) -> None:
        """Add recommendations based on failed checks."""
        recommendations = []

        for check in self.verification_results["checks"]:
            if not check["passed"]:
                if "Component" in check["name"]:
                    component = check["name"].split(":")[1].strip()
                    recommendations.append(f"Fix issues with {component} component")
                elif "Error Count" in check["name"]:
                    recommendations.append("Address all errors reported in the simulation")
                elif "Transaction Success Rate" in check["name"]:
                    recommendations.append("Improve transaction success rate by addressing failed transactions")
                elif "Circuit Breaker" in check["name"]:
                    recommendations.append("Investigate and fix issues causing circuit breaker trips")
                elif "API Success Rate" in check["name"]:
                    recommendations.append("Improve API reliability or implement better fallback mechanisms")
                elif "Simulation Duration" in check["name"]:
                    recommendations.append("Investigate timing issues in the simulation")

        self.verification_results["recommendations"] = recommendations

    def print_report(self) -> None:
        """Print the verification report."""
        print(f"\n{BLUE}======================================{NC}")
        print(f"{BLUE}SYNERGY7 SYSTEM - SIMULATION VERIFICATION REPORT{NC}")
        print(f"{BLUE}======================================{NC}\n")

        # Print overall status
        status = self.verification_results["overall_status"]
        score = self.verification_results["score"]
        max_score = self.verification_results["max_score"]
        score_pct = (score / max_score) * 100 if max_score > 0 else 0

        if status == "PASSED":
            color = GREEN
        elif status == "WARNING":
            color = YELLOW
        else:
            color = RED

        print(f"{color}OVERALL STATUS: {status}{NC}")
        print(f"{color}SCORE: {score}/{max_score} ({score_pct:.1f}%){NC}\n")

        # Print checks
        print(f"{BLUE}VERIFICATION CHECKS:{NC}")
        for check in self.verification_results["checks"]:
            status = f"{GREEN}✓{NC}" if check["passed"] else f"{RED}✗{NC}"
            print(f"  {status} {check['name']}: {check['description']} ({check['points']} points)")

        print()

        # Print recommendations
        if self.verification_results["recommendations"]:
            print(f"{YELLOW}RECOMMENDATIONS:{NC}")
            for i, recommendation in enumerate(self.verification_results["recommendations"], 1):
                print(f"{YELLOW}{i}. {recommendation}{NC}")
            print()

        print(f"{BLUE}======================================{NC}")

    def save_report(self, output_file: str) -> bool:
        """
        Save the verification report to a file.

        Args:
            output_file: Path to output file

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(output_file, "w") as f:
                json.dump(self.verification_results, f, indent=2)

            logger.info(f"Verification report saved to {output_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving verification report: {str(e)}")
            return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Verify simulation results for Synergy7 Trading System")
    parser.add_argument("results_file", help="Path to simulation results file")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--output", help="Path to output verification report")

    args = parser.parse_args()

    # Create verifier
    verifier = SimulationVerifier(args.results_file, args.config)

    # Load data
    if not verifier.load_data():
        logger.error("Failed to load data")
        return 1

    # Verify results
    verifier.verify()

    # Print report
    verifier.print_report()

    # Save report if output file specified
    if args.output:
        verifier.save_report(args.output)

    # Return exit code based on verification status
    return 0 if verifier.verification_results["overall_status"] == "PASSED" else 1

if __name__ == "__main__":
    sys.exit(main())
