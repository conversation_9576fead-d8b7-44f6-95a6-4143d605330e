#!/bin/bash
# Run dashboard test for Synergy7 Trading System

# Set default values
CONFIG_FILE="config.yaml"
OUTPUT_DIR="output/dashboard_tests"
DURATION=60
DASHBOARD_PORT=8501
HEALTH_PORT=8080

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --config)
      CONFIG_FILE="$2"
      shift 2
      ;;
    --output)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --duration)
      DURATION="$2"
      shift 2
      ;;
    --dashboard-port)
      DASHBOARD_PORT="$2"
      shift 2
      ;;
    --health-port)
      HEALTH_PORT="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --config FILE           Path to configuration file (default: config.yaml)"
      echo "  --output DIR            Directory to store test results (default: output/dashboard_tests)"
      echo "  --duration SEC          Test duration in seconds (default: 60)"
      echo "  --dashboard-port PORT   Port for the Streamlit dashboard (default: 8501)"
      echo "  --health-port PORT      Port for the health server (default: 8080)"
      echo "  --help                  Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if required packages are installed
echo "Checking required packages..."
MISSING_PACKAGES=()

# Check for Python packages
python -c "import streamlit" 2>/dev/null || MISSING_PACKAGES+=("streamlit")
python -c "import requests" 2>/dev/null || MISSING_PACKAGES+=("requests")
python -c "import pandas" 2>/dev/null || MISSING_PACKAGES+=("pandas")
python -c "import numpy" 2>/dev/null || MISSING_PACKAGES+=("numpy")
python -c "import plotly" 2>/dev/null || MISSING_PACKAGES+=("plotly")

if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
  echo "Missing required packages: ${MISSING_PACKAGES[*]}"
  echo "Installing missing packages..."
  pip install ${MISSING_PACKAGES[*]}
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Make sure the script is executable
chmod +x phase_4_deployment/scripts/test_dashboard.py

# Create output directory for dashboard simulator
mkdir -p output/dashboard

# Run the dashboard test
echo "Running dashboard test with config: $CONFIG_FILE, output: $OUTPUT_DIR, duration: $DURATION seconds"
echo "Dashboard port: $DASHBOARD_PORT, Health port: $HEALTH_PORT"
python phase_4_deployment/scripts/test_dashboard.py \
  --config "$CONFIG_FILE" \
  --output "$OUTPUT_DIR" \
  --duration "$DURATION" \
  --dashboard-port "$DASHBOARD_PORT" \
  --health-port "$HEALTH_PORT"

# Check exit code
EXIT_CODE=$?

# Display Streamlit logs
STREAMLIT_LOG="$OUTPUT_DIR/streamlit.log"
if [ -f "$STREAMLIT_LOG" ]; then
  echo "=== Streamlit Logs ==="
  cat "$STREAMLIT_LOG"
  echo "======================"
fi

if [ $EXIT_CODE -eq 0 ]; then
  echo "Dashboard test completed successfully"
  exit 0
else
  echo "Dashboard test failed"
  exit 1
fi
